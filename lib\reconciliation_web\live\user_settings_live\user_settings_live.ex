defmodule ReconciliationWeb.UserSettingsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.Accounts

  def render(assigns) do
    ~H"""
    <style>
      :root {
        /* ProBASE Professional Color Scheme - Light Theme */
        --primary-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        --secondary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        --accent-gradient: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);

        /* Background Colors */
        --light-bg: #ffffff;
        --lighter-bg: #f8fafc;
        --section-bg: #f1f5f9;
        --dark-bg: #e2e8f0;
        --darker-bg: #cbd5e1;
        --content-bg: #ffffff;

        /* Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-bg-dark: rgba(248, 250, 252, 0.95);
        --glass-border: rgba(7, 37, 80, 0.2);
        --glass-border-light: rgba(226, 232, 240, 0.8);

        /* Text Colors */
        --text-primary: #0d1421;
        --text-primary-light: #1e293b;
        --text-secondary: #64748b;
        --text-secondary-light: #475569;

        /* ProBASE Brand Colors */
        --probase-primary: #1a237e;
        --probase-secondary: #f97316;
        --probase-accent: #3f51b5;
        --probase-dark: #0d1421;
        --probase-light: #f8fafc;
        --probase-gray: #64748b;
      }

      /* Settings Button Styling */
      .settings-button {
        width: auto;
        min-width: 180px;
        padding: 0.75rem 2rem;
        background: var(--secondary-gradient);
        color: white;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(249, 115, 22, 0.2);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 0.5rem;
      }

      .settings-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(249, 115, 22, 0.3);
      }

      .settings-button:active {
        transform: translateY(0);
      }

      .settings-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }

      /* Settings Page Styling */
      .settings-container {
        max-width: 800px;
        margin: 1rem auto;
        padding: 1.5rem;
        background: var(--glass-bg);
        border-radius: 16px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        border: 1px solid var(--glass-border-light);
        backdrop-filter: blur(20px);
      }

      .settings-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
        text-align: center;
      }

      .settings-subtitle {
        color: var(--text-secondary);
        text-align: center;
        margin-bottom: 1.5rem;
        font-size: 1rem;
      }

      .settings-sections {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
      }

      .settings-section {
        padding: 1.25rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 12px;
        border: 1px solid rgba(226, 232, 240, 0.8);
        max-width: 400px;
        width: 100%;
      }

      .settings-section h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.75rem;
      }

      /* Compact form styling */
      .settings-section .form-group {
        margin-bottom: 1rem;
      }

      .settings-section label {
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
      }

      .settings-section input {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .settings-sections {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .settings-container {
          margin: 0.5rem;
          padding: 1rem;
        }
      }
    </style>

    <div class="settings-container">
      <h1 class="settings-title">Account Settings</h1>
      <p class="settings-subtitle">Manage your account email address and password settings</p>

      <div class="settings-sections">
        <%!-- <div class="settings-section">
          <h3>Change Email Address</h3>
          <.simple_form
            for={@email_form}
            id="email_form"
            phx-submit="update_email"
            phx-change="validate_email"
          >
            <.input field={@email_form[:email]} type="email" label="New Email Address" required />
            <.input
              field={@email_form[:current_password]}
              name="current_password"
              id="current_password_for_email"
              type="password"
              label="Current password"
              value={@email_form_current_password}
              required
            />
            <:actions>
              <.button phx-disable-with="Changing..." class="settings-button">Change Email</.button>
            </:actions>
          </.simple_form>
        </div> --%>

        <div class="settings-section">
          <h3>Change Password</h3>
          <.simple_form
            for={@password_form}
            id="password_form"
            action={~p"/users/log_in?_action=password_updated"}
            method="post"
            phx-change="validate_password"
            phx-submit="update_password"
            phx-trigger-action={@trigger_submit}
          >
            <input
              name={@password_form[:email].name}
              type="hidden"
              id="hidden_user_email"
              value={@current_email}
            />
            <.input field={@password_form[:password]} type="password" label="New password" required />
            <.input
              field={@password_form[:password_confirmation]}
              type="password"
              label="Confirm new password"
            />
            <.input
              field={@password_form[:current_password]}
              name="current_password"
              type="password"
              label="Current password"
              id="current_password_for_password"
              value={@current_password}
              required
            />
            <:actions>
              <.button phx-disable-with="Changing..." class="settings-button">Change Password</.button>
            </:actions>
          </.simple_form>
        </div>
      </div>
    </div>
    """
  end

  def mount(%{"token" => token}, _session, socket) do
    socket =
      case Accounts.update_user_email(socket.assigns.current_user, token) do
        :ok ->
          put_flash(socket, :info, "Email changed successfully.")

        :error ->
          put_flash(socket, :error, "Email change link is invalid or it has expired.")
      end

    {:ok, push_navigate(socket, to: ~p"/users/settings")}
  end

  def mount(_params, _session, socket) do
    user = socket.assigns.current_user
    email_changeset = Accounts.change_user_email(user)
    password_changeset = Accounts.change_user_password(user)

    socket =
      socket
      |> assign(:page_title, "Account Settings")
      |> assign(:current_password, nil)
      |> assign(:email_form_current_password, nil)
      |> assign(:current_email, user.email)
      |> assign(:email_form, to_form(email_changeset))
      |> assign(:password_form, to_form(password_changeset))
      |> assign(:trigger_submit, false)

    {:ok, socket}
  end

  def handle_event("validate_email", params, socket) do
    %{"current_password" => password, "user" => user_params} = params

    email_form =
      socket.assigns.current_user
      |> Accounts.change_user_email(user_params)
      |> Map.put(:action, :validate)
      |> to_form()

    {:noreply, assign(socket, email_form: email_form, email_form_current_password: password)}
  end

  def handle_event("update_email", params, socket) do
    %{"current_password" => password, "user" => user_params} = params
    user = socket.assigns.current_user

    case Accounts.apply_user_email(user, password, user_params) do
      {:ok, applied_user} ->
        Accounts.deliver_user_update_email_instructions(
          applied_user,
          user.email,
          &url(~p"/users/settings/confirm_email/#{&1}")
        )

        info = "A link to confirm your email change has been sent to the new address."
        {:noreply, socket |> put_flash(:info, info) |> assign(email_form_current_password: nil)}

      {:error, changeset} ->
        {:noreply, assign(socket, :email_form, to_form(Map.put(changeset, :action, :insert)))}
    end
  end

  def handle_event("validate_password", params, socket) do
    %{"current_password" => password, "user" => user_params} = params

    password_form =
      socket.assigns.current_user
      |> Accounts.change_user_password(user_params)
      |> Map.put(:action, :validate)
      |> to_form()

    {:noreply, assign(socket, password_form: password_form, current_password: password)}
  end

  def handle_event("update_password", params, socket) do
    %{"current_password" => password, "user" => user_params} = params
    user = socket.assigns.current_user

    case Accounts.update_user_password(user, password, user_params) do
      {:ok, user} ->
        password_form =
          user
          |> Accounts.change_user_password(user_params)
          |> to_form()

        socket =
          socket
          |> put_flash(:info, "✅ Password changed successfully! You will be logged in with your new password.")
          |> push_event("show_toast", %{
            type: "success",
            title: "Password Updated!",
            message: "Your password has been changed successfully. You will be logged in with your new password.",
            duration: 5000
          })
          |> assign(trigger_submit: true, password_form: password_form)

        {:noreply, socket}

      {:error, changeset} ->
        # Extract specific error messages from the changeset
        error_messages =
          changeset.errors
          |> Enum.map(fn {field, {message, _}} ->
            case field do
              :current_password -> "Current password is incorrect"
              :password -> "New password #{message}"
              :password_confirmation -> "Password confirmation #{message}"
              _ -> "#{field} #{message}"
            end
          end)
          |> Enum.join(", ")

        error_message = if error_messages != "", do: error_messages, else: "Password change failed. Please check your input and try again."

        socket =
          socket
          |> put_flash(:error, "❌ Password change failed: #{error_message}")
          |> push_event("show_toast", %{
            type: "error",
            title: "Password Change Failed",
            message: error_message,
            duration: 6000
          })
          |> assign(password_form: to_form(changeset))

        {:noreply, socket}
    end
  end
end
